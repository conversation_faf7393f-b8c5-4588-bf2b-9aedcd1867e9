/* Main container for the carousel */
.outer-carousel-container {
    position: relative;
    width: 100%;
    max-width: 100%;
    padding: 0;
    box-sizing: border-box;
    margin: 0 auto;
}

/* Inner container */
.carousel-container {
    position: relative;
    width: 100%;
    overflow: visible;
}

/* The carousel itself */
.skills-carousel {
    position: relative;
    width: 100%;
    margin: 0 auto;
}

/* Navigation buttons wrapper */
.nav-buttons-wrapper {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 1000 !important;
    width: 50px !important;
    height: 50px !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Position the left button */
.nav-buttons-wrapper:first-of-type {
    left: -50px !important;
}

/* Position the right button */
.nav-buttons-wrapper:last-of-type {
    right: -50px !important;
}

/* Style the navigation buttons */
.carousel-nav-btn {
    background: var(--glass-hover) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 50% !important;
    width: 45px !important;
    height: 45px !important;
    border: 1px solid var(--glass-border) !important;
    box-shadow: none !important;
    transition: all 0.2s ease !important;
    color: var(--icon-default) !important;
    font-size: 1.5rem !important;
}

.carousel-nav-btn:hover {
    background: var(--glass-active) !important;
    border-color: var(--glass-border-hover) !important;
    color: var(--icon-hover) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3) !important;
}

/* Carousel slide - Professional spacing aligned with homepage-container */
.carousel-slide {
    padding: var(--spacing-xl) var(--spacing-xl); /* 2rem = 32px */
    box-sizing: border-box;
    min-height: 380px; /* Consistent height */
    height: 380px; /* Fixed height for consistency */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 95%;
    margin: 0 auto;
    overflow: hidden; /* Prevent content overflow */
}

/* Indicator container */
.carousel-indicators {
    margin-top: 20px !important;
    margin-bottom: 10px !important;
    display: flex !important;
    justify-content: center !important;
}

/* Removed forced first-child active state - this was causing the bug */

/* Indicator buttons */
.MuiButton-root {
    min-width: 10px !important;
    width: 10px !important;
    height: 10px !important;
    padding: 6px !important;
    margin: 6px 4px !important;
    border-radius: 50% !important;
    background: var(--glass-base) !important;
    border: 1px solid var(--glass-border) !important;
    backdrop-filter: blur(15px) !important;
    transition: all var(--transition-normal) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.MuiButton-root:hover {
    background: var(--glass-hover) !important;
    border-color: var(--glass-border-hover) !important;
    transform: scale(1.15) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Active state for carousel indicators - Monochromatic design with high contrast */
.MuiButton-root.active,
.MuiButton-root[aria-pressed="true"],
.MuiButton-root.Mui-selected,
.MuiButton-root.MuiButton-contained,
.carousel-indicators .MuiButton-root[aria-current="true"],
.carousel-indicators .MuiButton-root[data-testid*="active"],
.carousel-indicators .MuiButton-root:focus,
.carousel-indicators .MuiButton-root[tabindex="0"],
/* Additional react-material-ui-carousel specific selectors */
.carousel-indicators .MuiButton-root[class*="active"],
.carousel-indicators .MuiButton-root[class*="selected"],
.skills-carousel .MuiButton-root[aria-selected="true"],
.skills-carousel .MuiButton-root.MuiButton-containedPrimary,
.carousel-indicators .MuiButton-root.carousel-active,
.carousel-indicators .MuiButton-root[data-active="true"] {
    background: #27272a !important;
    border-color: #27272a !important;
    transform: scale(1.4) !important;
    box-shadow:
        0 4px 20px rgba(39, 39, 42, 0.5) !important,
        0 2px 10px rgba(39, 39, 42, 0.3) !important,
        0 0 0 3px rgba(255, 255, 255, 0.4) !important;
    opacity: 1 !important;
}

/* Focus states for accessibility compliance */
.MuiButton-root:focus-visible {
    outline: 2px solid #27272a !important;
    outline-offset: 2px !important;
}

.dark .MuiButton-root:focus-visible {
    outline: 2px solid #f1f5f9 !important;
    outline-offset: 2px !important;
}

/* Make sure the carousel paper has no background */
.MuiPaper-root {
    background-color: transparent !important;
    box-shadow: none !important;
}

/* Ensure the projects list container is properly sized */
.projects-list {
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: visible;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Media query for smaller screens */
@media (max-width: 768px) {
    .outer-carousel-container {
        width: 100%;
        padding: 0;
    }

    .projects-list {
        padding: 0;
    }

    .nav-buttons-wrapper:first-of-type {
        left: -25px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -25px !important;
    }

    .carousel-nav-btn {
        width: 35px !important;
        height: 35px !important;
        font-size: 1.1rem !important;
        background: var(--glass-active) !important;
    }

    .carousel-slide {
        min-height: 400px;
        height: 400px; /* Fixed height for consistency */
        padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px - reduced for more content space */
    }
}

/* Media query for very small screens (mobile phones) */
@media (max-width: 480px) {
    .nav-buttons-wrapper:first-of-type {
        left: -15px !important;
    }

    .nav-buttons-wrapper:last-of-type {
        right: -15px !important;
    }

    .carousel-nav-btn {
        width: 30px !important;
        height: 30px !important;
        font-size: 1rem !important;
    }

    .carousel-slide {
        min-height: 420px;
        height: 420px;
        padding: var(--spacing-lg) var(--spacing-lg); /* 1.5rem = 24px */
    }

    /* Responsive carousel dots for mobile */
    .MuiButton-root {
        min-width: 8px !important;
        width: 8px !important;
        height: 8px !important;
        padding: 5px !important;
        margin: 4px 3px !important;
    }

    /* Mobile active state scaling */
    .MuiButton-root.active,
    .MuiButton-root.carousel-active,
    .MuiButton-root[data-active="true"] {
        transform: scale(1.3) !important;
    }

    .dark .MuiButton-root.active,
    .dark .MuiButton-root.carousel-active,
    .dark .MuiButton-root[data-active="true"] {
        transform: scale(1.3) !important;
    }
}

/* Dark mode styles */
.dark .carousel-nav-btn {
    background: var(--dark-glass-hover) !important;
    border-color: var(--dark-glass-border) !important;
    color: var(--dark-icon-default) !important;
}

.dark .carousel-nav-btn:hover {
    background: var(--dark-glass-active) !important;
    border-color: var(--dark-glass-border-hover) !important;
    color: var(--dark-icon-hover) !important;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2) !important;
}

.dark .MuiButton-root {
    background: rgba(71, 85, 105, 0.3) !important;
    border-color: rgba(100, 116, 139, 0.4) !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.dark .MuiButton-root:hover {
    background: var(--dark-glass-hover) !important;
    border-color: var(--dark-glass-border-hover) !important;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.25) !important,
        0 0 15px rgba(255, 255, 255, 0.1) !important;
    transform: scale(1.15) !important;
}

/* Dark mode active state for carousel indicators - High contrast monochromatic design */
.dark .MuiButton-root.active,
.dark .MuiButton-root[aria-pressed="true"],
.dark .MuiButton-root.Mui-selected,
.dark .MuiButton-root.MuiButton-contained,
.dark .carousel-indicators .MuiButton-root[aria-current="true"],
.dark .carousel-indicators .MuiButton-root[data-testid*="active"],
.dark .carousel-indicators .MuiButton-root:focus,
.dark .carousel-indicators .MuiButton-root[tabindex="0"],
/* Additional react-material-ui-carousel specific selectors for dark mode */
.dark .carousel-indicators .MuiButton-root[class*="active"],
.dark .carousel-indicators .MuiButton-root[class*="selected"],
.dark .skills-carousel .MuiButton-root[aria-selected="true"],
.dark .skills-carousel .MuiButton-root.MuiButton-containedPrimary,
.dark .carousel-indicators .MuiButton-root.carousel-active,
.dark .carousel-indicators .MuiButton-root[data-active="true"] {
    background: #f1f5f9 !important;
    border-color: #f1f5f9 !important;
    transform: scale(1.4) !important;
    box-shadow:
        0 4px 20px rgba(241, 245, 249, 0.4) !important,
        0 2px 10px rgba(241, 245, 249, 0.3) !important,
        0 0 0 3px rgba(51, 65, 85, 0.6) !important;
    opacity: 1 !important;
}
