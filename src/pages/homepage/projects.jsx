import {motion} from 'motion/react';
import INFO from '../../data/user.js';
import React, { useState, useEffect } from 'react';
import Carousel from 'react-material-ui-carousel';
import Project from '../../components/projects/project.jsx';
import '../styles/carousel.css';

function Projects() {
	const [activeIndex, setActiveIndex] = useState(0);

	// Update active indicator styling when carousel changes
	useEffect(() => {
		const updateActiveIndicator = () => {
			const indicators = document.querySelectorAll('.carousel-indicators .MuiButton-root');
			indicators.forEach((indicator, index) => {
				if (index === activeIndex) {
					indicator.classList.add('carousel-active');
					indicator.setAttribute('aria-selected', 'true');
				} else {
					indicator.classList.remove('carousel-active');
					indicator.setAttribute('aria-selected', 'false');
				}
			});
		};

		// Small delay to ensure DOM is updated
		const timer = setTimeout(updateActiveIndicator, 100);
		return () => clearTimeout(timer);
	}, [activeIndex]);

	const handleCarouselChange = (index) => {
		setActiveIndex(index);
	};

	return (
		<React.Fragment>
			<section id="projects" className="scroll-child">
				<div className="white-space"></div>
				<motion.div initial={{opacity: 0}}
				            whileInView={{opacity: 1}}
				            transition={{duration: 0.75}}
				            className="homepage-container bg-blur">
					<div className="title projects-title">
						<h2>Skills</h2>
					</div>

					<div className="projects-list" role="region" aria-label="Skills carousel">
						<Carousel
							navButtonsAlwaysVisible={true}
							navButtonsProps={{
								className: "carousel-nav-btn",
								style: {
									backgroundColor: "rgba(197, 197, 197, 0.7)",
									color: "#27272a",
									padding: 0
								}
							}}
							navButtonsWrapperProps={{
								className: "nav-buttons-wrapper"
							}}
							indicatorContainerProps={{
								className: "carousel-indicators"
							}}
							indicatorIconButtonProps={{
								style: {
									padding: '6px',
									margin: '6px 4px'
								}
							}}
							className="skills-carousel"
							animation="slide"
							autoPlay={false}
							interval={6000}
							fullHeightHover={false}
							onChange={handleCarouselChange}
							index={activeIndex}
							style={{ width: '100%' }}
						>
							{INFO.projects.map((project, index) => (
								<div className="carousel-slide" key={index}>
									<Project
										logo={project.logo}
										title={project.title}
										description={project.description}
									/>
								</div>
							))}
						</Carousel>
					</div>
				</motion.div>
			</section>
		</React.Fragment>
	);
}

export default Projects;
