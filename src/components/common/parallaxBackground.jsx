import React, { useEffect, useCallback, useRef } from 'react';

const ParallaxBackground = () => {
    const rafRef = useRef(null);
    const lastScrollY = useRef(0);

    const updateBackgroundPosition = useCallback(() => {
        // Get the content wrapper element - this is our scroll container
        const contentWrapper = document.querySelector('#content-wrapper') || document.querySelector('.content-wrapper');
        
        if (!contentWrapper) {
            return;
        }

        const scrollTop = contentWrapper.scrollTop;
        const scrollHeight = contentWrapper.scrollHeight;
        const clientHeight = contentWrapper.clientHeight;
        
        // Calculate scroll progress (0 to 1)
        const maxScroll = scrollHeight - clientHeight;
        const scrollProgress = maxScroll > 0 ? Math.min(scrollTop / maxScroll, 1) : 0;
        
        // Only update if scroll position has changed significantly
        if (Math.abs(scrollTop - lastScrollY.current) < 1) {
            return;
        }
        
        lastScrollY.current = scrollTop;
        
        // Calculate background positions based on scroll progress
        // Light mode: Move background from 0% to 100% horizontally as user scrolls
        const lightBgPosition = `${scrollProgress * 100}% 50%`;
        
        // Dark mode: Move background diagonally for more dynamic effect
        const darkBgPositionX = scrollProgress * 100;
        const darkBgPositionY = 50 + (scrollProgress * 50); // Move from 50% to 100% vertically
        const darkBgPosition = `${darkBgPositionX}% ${darkBgPositionY}%`;
        
        // App layer: Move in opposite direction for parallax effect
        const appBgPositionX = 100 - (scrollProgress * 100);
        const appBgPositionY = scrollProgress * 100;
        const appBgPosition = `${appBgPositionX}% ${appBgPositionY}%`;
        
        // Apply to HTML background (base layer)
        const htmlElement = document.documentElement;
        if (htmlElement) {
            htmlElement.style.backgroundPosition = lightBgPosition;
        }
        
        // Apply to App background (overlay layer)
        const appElement = document.querySelector('.App');
        if (appElement) {
            appElement.style.backgroundPosition = appBgPosition;
        }
        
        // Handle dark mode backgrounds
        const isDarkMode = document.body.classList.contains('dark');
        if (isDarkMode) {
            if (htmlElement) {
                htmlElement.style.backgroundPosition = darkBgPosition;
            }
        }
    }, []);

    const handleScroll = useCallback(() => {
        // Cancel any pending animation frame
        if (rafRef.current) {
            cancelAnimationFrame(rafRef.current);
        }
        
        // Schedule the background update for the next frame
        rafRef.current = requestAnimationFrame(updateBackgroundPosition);
    }, [updateBackgroundPosition]);

    useEffect(() => {
        // Get the content wrapper element
        const contentWrapper = document.querySelector('#content-wrapper') || document.querySelector('.content-wrapper');
        
        if (!contentWrapper) {
            console.warn('Content wrapper not found for parallax background');
            return;
        }

        // Initial background position
        updateBackgroundPosition();

        // Add scroll listener with passive option for better performance
        contentWrapper.addEventListener('scroll', handleScroll, { passive: true });

        // Handle resize events that might affect scroll calculation
        const handleResize = () => {
            setTimeout(updateBackgroundPosition, 100);
        };
        window.addEventListener('resize', handleResize, { passive: true });

        return () => {
            // Cleanup
            contentWrapper.removeEventListener('scroll', handleScroll);
            window.removeEventListener('resize', handleResize);
            
            if (rafRef.current) {
                cancelAnimationFrame(rafRef.current);
            }
        };
    }, [handleScroll, updateBackgroundPosition]);

    // This component doesn't render anything visible
    return null;
};

export default ParallaxBackground;
