@import "../../../data/styles.css";

.project {
    mix-blend-mode: normal;
    min-height: 320px; /* Consistent minimum height */
    height: 320px; /* Fixed height for consistency */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    transition: transform var(--transition-normal);
    padding: var(--spacing-lg) var(--spacing-md); /* 1.5rem 1rem = 24px 16px */
    margin: 0 auto;
    overflow: hidden; /* Prevent content overflow */
}

.project a {
    text-decoration: none;
}

.project-container{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    justify-content: flex-start;
    align-items: stretch;
}



.all-project-container {
    height: 100%;
    padding: 2px;
}

.project-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 12px;
    flex-shrink: 0; /* Prevent header from shrinking */
    min-height: 50px; /* Consistent header height */
}

.project-logo {
    width: 50px;
    height: 50px;
    padding-right: 15px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.project-title {
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.project-title h3 {
    font-size: 1.4rem;
    margin: 0;
    color: var(--primary-color);
    font-weight: 600;
    line-height: 1.2;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Dark mode project styling */
.dark .project-title h3 {
    color: var(--dark-primary-text);
}

.dark .project-description {
    color: var(--dark-secondary-text);
}

.dark .project-content .subtitle {
    color: var(--dark-secondary-text);
}

.dark .project-description-list li {
    color: var(--dark-tertiary-text);
}

.dark .project-link {
    color: var(--dark-secondary-text);
}

.dark .project-link-text {
    color: var(--dark-secondary-text);
}

.project-content {
    width: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 8px;
    min-height: 0; /* Allow flex item to shrink */
}


.project-description {
    color: var(--secondary-color);
}

/* Subtitle styling for project description title */
.project-content .subtitle {
    color: var(--secondary-color);
    font-size: 0.95rem;
    font-weight: 500;
    margin: 0 0 8px 0;
    line-height: 1.3;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.project-link {
    display: flex;
    align-items: center;
    color: var(--secondary-color);
    font-size: 12px;
}

.project-link-icon {
    padding-left: 5px;
    font-size: 13px;
}

.project-link-text {
    padding-left: 20px;
    font-weight: 700;
}

.project-description-list {
    padding-left: 18px;
    margin: 8px 0 0 0;
    list-style-type: disc;
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.4;
    max-height: calc(100% - 20px); /* Prevent overflow */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.project-description-list::-webkit-scrollbar {
    display: none;
}

.project-description-list li {
    margin-bottom: 6px;
    line-height: 1.4;
    position: relative;
    padding-left: 3px;
    color: var(--secondary-color);
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

@media (max-width: 768px) {
    .project {
        min-height: 340px;
        height: 340px;
        padding: 12px 8px;
    }

    .project-title h3 {
        font-size: 1.3rem;
    }

    .project-content .subtitle {
        font-size: 0.9rem;
    }

    .project-description-list {
        font-size: 0.85rem;
        padding-left: 16px;
    }
}

@media (max-width: 480px) {
    .project {
        min-height: 360px;
        height: 360px;
        padding: 10px 6px;
    }

    .project-logo {
        width: 40px;
        height: 40px;
        padding-right: 12px;
    }

    .project-title h3 {
        font-size: 1.2rem;
    }

    .project-content .subtitle {
        font-size: 0.85rem;
        margin-bottom: 6px;
    }

    .project-description-list {
        font-size: 0.8rem;
        padding-left: 14px;
        margin-top: 6px;
    }

    .project-description-list li {
        margin-bottom: 5px;
    }
}

@media (max-width: 380px) {
    .project {
        min-height: 380px;
        height: 380px;
        padding: 8px 4px;
    }

    .project-logo {
        width: 35px;
        height: 35px;
        padding-right: 10px;
    }

    .project-title h3 {
        font-size: 1.1rem;
    }

    .project-content .subtitle {
        font-size: 0.8rem;
    }

    .project-description-list {
        font-size: 0.75rem;
        padding-left: 12px;
    }

    .project-description-list li {
        margin-bottom: 4px;
        line-height: 1.3;
    }
}
