@import "../../../data/styles.css";

.project {
    mix-blend-mode: normal;
    min-height: 300px; /* Reduced for better space utilization */
    height: 300px; /* Fixed height for consistency */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    transition: transform var(--transition-normal);
    padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px - more horizontal padding */
    margin: 0 auto;
    overflow: hidden; /* Prevent content overflow */
}

.project a {
    text-decoration: none;
}

.project-container{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    justify-content: flex-start;
    align-items: stretch;
}



.all-project-container {
    height: 100%;
    padding: 2px;
}

.project-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - reduced for better space utilization */
    flex-shrink: 0; /* Prevent header from shrinking */
    min-height: 45px; /* Slightly reduced header height */
}

.project-logo {
    width: 50px;
    height: 50px;
    padding-right: var(--spacing-lg); /* 1.5rem = 24px */
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.project-title {
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.project-title h3 {
    font-family: var(--secondary-font); /* Montserrat for headings */
    font-size: 1.4rem; /* Slightly reduced for better space utilization */
    margin: 0;
    color: var(--primary-color);
    font-weight: var(--heading-weight); /* 600 */
    line-height: 1.1; /* Tighter line height for better space efficiency */
    letter-spacing: -0.02em; /* Consistent with global h3 */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Dark mode project styling */
.dark .project-title h3 {
    color: var(--dark-primary-text);
}

.dark .project-description {
    color: var(--dark-secondary-text);
}

.dark .project-content .subtitle {
    color: var(--dark-secondary-text);
}

.dark .project-description-list li {
    color: var(--dark-tertiary-text);
}

.dark .project-link {
    color: var(--dark-secondary-text);
}

.dark .project-link-text {
    color: var(--dark-secondary-text);
}

.project-content {
    width: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 var(--spacing-sm); /* 0.5rem = 8px */
    min-height: 0; /* Allow flex item to shrink */
    max-height: calc(100% - 60px); /* Ensure content doesn't overflow container */
}


.project-description {
    color: var(--secondary-color);
}

/* Subtitle styling for project description title */
.project-content .subtitle {
    font-family: var(--primary-font); /* Poppins for body text */
    color: var(--secondary-color);
    font-size: 0.95rem; /* Optimized for better space utilization */
    font-weight: 500;
    margin: 0 0 var(--spacing-xs) 0; /* 0.25rem = 4px */
    line-height: 1.2; /* Tighter for better space efficiency */
    letter-spacing: 0.01em; /* Subtle letter spacing */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.project-link {
    display: flex;
    align-items: center;
    color: var(--secondary-color);
    font-size: 12px;
}

.project-link-icon {
    padding-left: 5px;
    font-size: 13px;
}

.project-link-text {
    padding-left: 20px;
    font-weight: 700;
}

.project-description-list {
    font-family: var(--primary-font); /* Poppins for body text */
    padding-left: var(--spacing-lg); /* 1.5rem = 24px */
    margin: var(--spacing-xs) 0 var(--spacing-xs) 0; /* 0.25rem = 4px - reduced margins */
    list-style-type: disc;
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1;
    font-size: 0.9rem; /* Optimized for better space utilization */
    line-height: 1.4; /* Balanced for readability and space efficiency */
    max-height: calc(100% - 30px); /* Optimized for reduced container height */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.project-description-list::-webkit-scrollbar {
    display: none;
}

.project-description-list li {
    margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
    line-height: 1.4; /* Balanced for readability and space efficiency */
    position: relative;
    padding-left: var(--spacing-xs); /* 0.25rem = 4px */
    color: var(--secondary-color);
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

@media (max-width: 768px) {
    .project {
        min-height: 340px;
        height: 340px;
        padding: var(--spacing-md) var(--spacing-sm); /* 1rem 0.5rem = 16px 8px */
    }

    .project-title h3 {
        font-size: 1.3rem; /* Slightly reduced for better fit */
        line-height: 1.2; /* Tighter line height */
    }

    .project-content .subtitle {
        font-size: 0.9rem; /* Reduced for better fit */
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        line-height: 1.2; /* Tighter line height */
    }

    .project-description-list {
        font-size: 0.85rem; /* Reduced for better fit */
        padding-left: var(--spacing-md); /* 1rem = 16px */
        line-height: 1.3; /* Tighter line height */
        margin: var(--spacing-xs) 0 var(--spacing-xs) 0; /* Reduced margins */
    }

    .project-description-list li {
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        line-height: 1.3; /* Consistent with parent */
    }
}

@media (max-width: 480px) {
    .project {
        min-height: 330px; /* Compact mobile layout optimized for swipe navigation */
        height: 330px;
        padding: var(--spacing-md) var(--spacing-lg); /* 1rem 1.5rem = 16px 24px - generous padding for full-width design */
    }

    .project-header {
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate margin for readability */
        min-height: 45px; /* Adequate header height for touch-friendly design */
    }

    .project-logo {
        width: 36px; /* Restored size for better visibility in full-width layout */
        height: 36px;
        padding-right: var(--spacing-md); /* 1rem = 16px - adequate spacing */
    }

    .project-title h3 {
        font-size: 1.2rem; /* Restored size for better hierarchy in full-width layout */
        line-height: 1.2; /* Balanced line height for readability */
    }

    .project-content {
        padding: 0 var(--spacing-sm); /* 0.5rem = 8px - adequate content padding */
        flex: 1; /* Use all available space */
    }

    .project-content .subtitle {
        font-size: 0.9rem; /* Improved readability for full-width layout */
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate margin */
        line-height: 1.3; /* Balanced line height */
    }

    .project-description-list {
        font-size: 0.85rem; /* Optimized for readability in full-width mobile layout */
        padding-left: var(--spacing-lg); /* 1.5rem = 24px - proper indentation */
        margin: var(--spacing-sm) 0 var(--spacing-sm) 0; /* Adequate margins */
        line-height: 1.4; /* Improved readability */
        max-height: calc(100% - 25px); /* Ensure content fits */
    }

    .project-description-list li {
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing */
        line-height: 1.4; /* Consistent with parent */
    }
}

/* Intermediate mobile breakpoint for smoother transitions */
@media (max-width: 420px) {
    .project {
        min-height: 325px; /* Intermediate height */
        height: 325px;
        padding: var(--spacing-xs) var(--spacing-sm); /* 0.25rem 0.5rem = 4px 8px */
    }

    .project-header {
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        min-height: 38px; /* Intermediate header height */
    }

    .project-logo {
        width: 30px; /* Intermediate size */
        height: 30px;
        padding-right: var(--spacing-xs); /* 0.25rem = 4px */
    }

    .project-title h3 {
        font-size: 1.05rem; /* Intermediate size */
        line-height: 1.1;
    }

    .project-content .subtitle {
        font-size: 0.82rem; /* Intermediate size */
        margin-bottom: var(--spacing-xs);
        line-height: 1.2;
    }

    .project-description-list {
        font-size: 0.78rem; /* Intermediate size */
        padding-left: var(--spacing-sm); /* 0.5rem = 8px */
        margin: var(--spacing-xs) 0 var(--spacing-xs) 0;
        line-height: 1.32;
        max-height: calc(100% - 18px);
    }

    .project-description-list li {
        margin-bottom: var(--spacing-xs);
        line-height: 1.32;
    }
}

@media (max-width: 380px) {
    .project {
        min-height: 320px; /* Further reduced for very compact layout */
        height: 320px;
        padding: var(--spacing-xs) var(--spacing-sm); /* 0.25rem 0.5rem = 4px 8px - minimal padding for maximum content space */
    }

    .project-header {
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px - minimal margin */
        min-height: 36px; /* Compact header height */
    }

    .project-logo {
        width: 28px; /* Compact size for maximum content space */
        height: 28px;
        padding-right: var(--spacing-xs); /* 0.25rem = 4px - minimal spacing */
    }

    .project-title h3 {
        font-size: 1rem; /* Compact but readable */
        line-height: 1.1; /* Tight line height for space efficiency */
    }

    .project-content {
        padding: 0 var(--spacing-xs); /* 0.25rem = 4px - minimal content padding */
        flex: 1; /* Use all available space */
        max-height: calc(100% - 50px); /* Ensure content fits within container */
    }

    .project-content .subtitle {
        font-size: 0.8rem; /* Compact but readable */
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px - minimal margin */
        line-height: 1.2; /* Tight line height */
    }

    .project-description-list {
        font-size: 0.75rem; /* Compact for very small screens */
        padding-left: var(--spacing-sm); /* 0.5rem = 8px - minimal indentation */
        margin: var(--spacing-xs) 0 0 0; /* Minimal top margin only */
        line-height: 1.3; /* Balanced for readability and space */
        max-height: calc(100% - 15px); /* Maximize content area */
    }

    .project-description-list li {
        margin-bottom: 3px; /* Minimal spacing for compact layout */
        line-height: 1.3; /* Consistent with parent */
    }
}
