@import "../../../data/styles.css";

.project {
    mix-blend-mode: normal;
    min-height: 320px; /* Consistent minimum height */
    height: 320px; /* Fixed height for consistency */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    transition: transform var(--transition-normal);
    padding: var(--spacing-lg) var(--spacing-md); /* 1.5rem 1rem = 24px 16px */
    margin: 0 auto;
    overflow: hidden; /* Prevent content overflow */
}

.project a {
    text-decoration: none;
}

.project-container{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    justify-content: flex-start;
    align-items: stretch;
}



.all-project-container {
    height: 100%;
    padding: 2px;
}

.project-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: var(--spacing-md); /* 1rem = 16px */
    flex-shrink: 0; /* Prevent header from shrinking */
    min-height: 50px; /* Consistent header height */
}

.project-logo {
    width: 50px;
    height: 50px;
    padding-right: var(--spacing-lg); /* 1.5rem = 24px */
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.project-title {
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.project-title h3 {
    font-family: var(--secondary-font); /* Montserrat for headings */
    font-size: 1.5rem; /* Aligned with h4 hierarchy */
    margin: 0;
    color: var(--primary-color);
    font-weight: var(--heading-weight); /* 600 */
    line-height: var(--heading-line-height); /* 1.2 */
    letter-spacing: -0.02em; /* Consistent with global h3 */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Dark mode project styling */
.dark .project-title h3 {
    color: var(--dark-primary-text);
}

.dark .project-description {
    color: var(--dark-secondary-text);
}

.dark .project-content .subtitle {
    color: var(--dark-secondary-text);
}

.dark .project-description-list li {
    color: var(--dark-tertiary-text);
}

.dark .project-link {
    color: var(--dark-secondary-text);
}

.dark .project-link-text {
    color: var(--dark-secondary-text);
}

.project-content {
    width: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 var(--spacing-sm); /* 0.5rem = 8px */
    min-height: 0; /* Allow flex item to shrink */
    max-height: calc(100% - 60px); /* Ensure content doesn't overflow container */
}


.project-description {
    color: var(--secondary-color);
}

/* Subtitle styling for project description title */
.project-content .subtitle {
    font-family: var(--primary-font); /* Poppins for body text */
    color: var(--secondary-color);
    font-size: 1rem; /* Slightly larger for better hierarchy */
    font-weight: 500;
    margin: 0 0 var(--spacing-xs) 0; /* 0.25rem = 4px - reduced margin */
    line-height: 1.3; /* Optimized for space efficiency */
    letter-spacing: 0.01em; /* Subtle letter spacing */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.project-link {
    display: flex;
    align-items: center;
    color: var(--secondary-color);
    font-size: 12px;
}

.project-link-icon {
    padding-left: 5px;
    font-size: 13px;
}

.project-link-text {
    padding-left: 20px;
    font-weight: 700;
}

.project-description-list {
    font-family: var(--primary-font); /* Poppins for body text */
    padding-left: var(--spacing-lg); /* 1.5rem = 24px */
    margin: var(--spacing-sm) 0 var(--spacing-sm) 0; /* 0.5rem = 8px top and bottom */
    list-style-type: disc;
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1;
    font-size: 0.95rem; /* Slightly larger for better readability */
    line-height: 1.5; /* Reduced from 1.6 to prevent overflow */
    max-height: calc(100% - 40px); /* More space for margins */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.project-description-list::-webkit-scrollbar {
    display: none;
}

.project-description-list li {
    margin-bottom: var(--spacing-xs); /* 0.25rem = 4px - reduced spacing */
    line-height: 1.4; /* Optimized for space efficiency */
    position: relative;
    padding-left: var(--spacing-xs); /* 0.25rem = 4px */
    color: var(--secondary-color);
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

@media (max-width: 768px) {
    .project {
        min-height: 340px;
        height: 340px;
        padding: var(--spacing-md) var(--spacing-sm); /* 1rem 0.5rem = 16px 8px */
    }

    .project-title h3 {
        font-size: 1.3rem; /* Slightly reduced for better fit */
        line-height: 1.2; /* Tighter line height */
    }

    .project-content .subtitle {
        font-size: 0.9rem; /* Reduced for better fit */
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        line-height: 1.2; /* Tighter line height */
    }

    .project-description-list {
        font-size: 0.85rem; /* Reduced for better fit */
        padding-left: var(--spacing-md); /* 1rem = 16px */
        line-height: 1.3; /* Tighter line height */
        margin: var(--spacing-xs) 0 var(--spacing-xs) 0; /* Reduced margins */
    }

    .project-description-list li {
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        line-height: 1.3; /* Consistent with parent */
    }
}

@media (max-width: 480px) {
    .project {
        min-height: 360px;
        height: 360px;
        padding: var(--spacing-md) var(--spacing-sm); /* 1rem 0.5rem = 16px 8px - better vertical spacing */
    }

    .project-header {
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate margin */
        min-height: 48px; /* Adequate header height */
    }

    .project-logo {
        width: 36px; /* Better size for visibility */
        height: 36px;
        padding-right: var(--spacing-md); /* 1rem = 16px - adequate spacing */
    }

    .project-title h3 {
        font-size: 1.2rem; /* Good balance of size and space */
        line-height: 1.2; /* Readable line height */
    }

    .project-content {
        padding: 0 var(--spacing-sm); /* 0.5rem = 8px - adequate content padding */
    }

    .project-content .subtitle {
        font-size: 0.9rem; /* Better readability */
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate margin */
        line-height: 1.3; /* Readable line height */
    }

    .project-description-list {
        font-size: 0.85rem; /* Good balance of size and readability */
        padding-left: var(--spacing-lg); /* 1.5rem = 24px - proper indentation */
        margin: var(--spacing-sm) 0 var(--spacing-sm) 0; /* Adequate margins */
        line-height: 1.4; /* Readable line height */
    }

    .project-description-list li {
        margin-bottom: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing */
        line-height: 1.4; /* Consistent with parent */
    }
}

@media (max-width: 380px) {
    .project {
        min-height: 380px;
        height: 380px;
        padding: var(--spacing-sm) var(--spacing-sm); /* 0.5rem = 8px - adequate padding for content protection */
    }

    .project-header {
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        min-height: 38px; /* Compact header height */
    }

    .project-logo {
        width: 28px; /* Compact size for maximum text space */
        height: 28px;
        padding-right: var(--spacing-sm); /* 0.5rem = 8px - adequate spacing */
    }

    .project-title h3 {
        font-size: 1.05rem; /* Balanced size for readability */
        line-height: 1.1; /* Tight line height */
    }

    .project-content {
        padding: 0 var(--spacing-xs); /* 0.25rem = 4px - minimal content padding */
        max-height: calc(100% - 50px); /* Ensure content fits within container */
    }

    .project-content .subtitle {
        font-size: 0.8rem; /* Compact but readable */
        margin-bottom: var(--spacing-xs); /* 0.25rem = 4px */
        line-height: 1.2; /* Balanced line height */
    }

    .project-description-list {
        font-size: 0.75rem; /* Compact but readable */
        padding-left: var(--spacing-sm); /* 0.5rem = 8px - adequate indentation */
        margin: var(--spacing-xs) 0 0 0; /* Minimal top margin */
        line-height: 1.25; /* Balanced line height for readability */
        max-height: calc(100% - 30px); /* Prevent overflow */
    }

    .project-description-list li {
        margin-bottom: 3px; /* Minimal but adequate spacing */
        line-height: 1.25; /* Consistent with parent */
    }
}
